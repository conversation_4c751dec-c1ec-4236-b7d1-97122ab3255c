---
type: "agent_requested"
description: "Testing strategies, unit/integration/e2e test patterns, test setup, mocking, CI/CD testing, or test automation for Rust/TypeScript"
---

# Testing Guidelines

## Rust

- **Unit**: `#[tokio::test]` with test database setup
- **Integration**: axum-test for API endpoints, WebSocket testing
- **Mocking**: mockall for LLM clients and external services

```rust
#[tokio::test]
async fn test_business_creation() {
    let pool = setup_test_db().await;
    let repo = BusinessRepository::new(pool);
    let result = repo.create_business(business_data).await;
    assert!(result.is_ok());
}
```

## TypeScript/SolidJS

- **Components**: vitest + @solidjs/testing-library
- **Stores**: Test state management and actions
- **API**: Mock fetch and Tauri invoke for cross-platform

```typescript
describe("BusinessProfile", () => {
  it("renders and handles updates", () => {
    render(() => (
      <BusinessProfile business={mockBusiness} onUpdate={onUpdate} />
    ));
    fireEvent.input(screen.getByLabelText("Name"), {
      target: { value: "Updated" },
    });
    expect(onUpdate).toHaveBeenCalled();
  });
});
```

## E2E & CI/CD

- **E2E**: Playwright for full business setup flow
- **CI**: GitHub Actions with PostgreSQL service
- **Test DB**: Separate schemas `test_vertoie`, `test_business_{org_id}`

```yaml
# GitHub Actions
services:
  postgres:
    image: postgres:15
steps:
  - run: cd core && cargo test
  - run: cd web && npm test
  - run: npx playwright test
```
