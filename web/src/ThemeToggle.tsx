import { useTheme } from './theme';
import './ThemeToggle.css';

export const ThemeToggle = () => {
  const { theme, setTheme, isDark } = useTheme();

  const handleToggle = () => {
    const currentTheme = theme();
    if (currentTheme === 'light') {
      setTheme('dark');
    } else if (currentTheme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const getIcon = () => {
    const currentTheme = theme();
    if (currentTheme === 'light') {
      return (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
          <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" stroke="currentColor" stroke-width="2"/>
        </svg>
      );
    } else if (currentTheme === 'dark') {
      return (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="2"/>
        </svg>
      );
    } else {
      return (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
          <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
          <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>
        </svg>
      );
    }
  };

  const getLabel = () => {
    const currentTheme = theme();
    if (currentTheme === 'light') return 'Switch to dark mode';
    if (currentTheme === 'dark') return 'Switch to system mode';
    return 'Switch to light mode';
  };

  return (
    <button
      class="theme-toggle"
      onClick={handleToggle}
      aria-label={getLabel()}
      title={getLabel()}
    >
      <span class="theme-toggle-icon" classList={{ 'is-dark': isDark() }}>
        {getIcon()}
      </span>
    </button>
  );
};
