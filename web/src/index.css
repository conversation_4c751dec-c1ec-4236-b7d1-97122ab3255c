:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Vertoie Brand Colors - CSS Variables */

  /* Orange Scale (Primary Brand) */
  --orange-50: #FFF4F0;
  --orange-100: #FFE5D9;
  --orange-200: #FFCAB0;
  --orange-300: #FFA87A;
  --orange-400: #FF8A56;
  --orange-500: #FF6B35; /* Primary brand color */
  --orange-600: #E65100;
  --orange-700: #CC4700;
  --orange-800: #B33E00;
  --orange-900: #992E00;

  /* Amber Scale (Secondary) */
  --amber-100: #FDF6E3;
  --amber-400: #FBBF24;
  --amber-500: #F7931E;
  --amber-600: #D97706;
  --amber-800: #92400E;

  /* Gray Scale (Neutrals) */
  --gray-50: #FAFAFA;
  --gray-100: #F4F4F5;
  --gray-200: #E4E4E7;
  --gray-300: #D4D4D8;
  --gray-400: #A1A1AA;
  --gray-500: #71717A;
  --gray-600: #52525B;
  --gray-700: #3F3F46;
  --gray-800: #27272A;
  --gray-900: #1F2937;
  --gray-950: #111827;

  /* Supporting Colors */
  --white: #FFFFFF;
  --warm-white: #FFF7F0;
  --light-peach: #FFE5D9;

  /* Semantic Colors */
  --success-light: #DCFCE7;
  --success: #22C55E;
  --success-dark: #16A34A;
  --warning-light: #FEF3C7;
  --warning: #F59E0B;
  --warning-dark: #D97706;
  --error-light: #FEE2E2;
  --error: #EF4444;
  --error-dark: #DC2626;
  --info-light: #DBEAFE;
  --info: #3B82F6;
  --info-dark: #2563EB;

  /* Theme Variables - Light Mode (Default) */
  --bg-primary: var(--gray-50);
  --bg-secondary: var(--white);
  --bg-tertiary: var(--warm-white);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-color-strong: rgba(0, 0, 0, 0.15);

  /* Brand gradients */
  --gradient-primary: linear-gradient(135deg, var(--orange-500) 0%, var(--amber-500) 100%);
  --gradient-hero: linear-gradient(135deg, var(--orange-500) 0%, var(--amber-500) 100%);

  color-scheme: light;
  color: var(--text-primary);
  background-color: var(--bg-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Dark Mode Theme Variables */
[data-theme="dark"] {
  --bg-primary: var(--gray-950);
  --bg-secondary: var(--gray-900);
  --bg-tertiary: var(--gray-800);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-muted: var(--gray-400);
  --border-light: var(--gray-700);
  --border-medium: var(--gray-600);
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-color-strong: rgba(0, 0, 0, 0.5);

  color-scheme: dark;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* System preference dark mode (fallback) */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --bg-primary: var(--gray-950);
    --bg-secondary: var(--gray-900);
    --bg-tertiary: var(--gray-800);
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);
    --border-light: var(--gray-700);
    --border-medium: var(--gray-600);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-color-strong: rgba(0, 0, 0, 0.5);

    color-scheme: dark;
    color: var(--text-primary);
    background-color: var(--bg-primary);
  }
}
