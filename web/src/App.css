* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Hero Section */
.hero {
  background: var(--gradient-hero);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(10px, 10px); }
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hero-content p {
  font-size: 1.25rem;
  color: rgba(255,255,255,0.95);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary {
  background: var(--white);
  color: var(--orange-500);
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 12px var(--shadow-color-strong);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-color-strong);
  background: var(--warm-white);
}

.btn-secondary {
  background: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255,255,255,0.1);
  border-color: white;
}

.hero-demo {
  background: rgba(255,255,255,0.1);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.2);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.demo-screen {
  background: var(--gray-900);
  border-radius: 12px;
  padding: 1.5rem;
  font-family: 'Courier New', monospace;
  color: var(--success);
  font-size: 0.9rem;
  line-height: 1.4;
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

/* Smooth content transitions when business type changes */
.demo-screen {
  transition: all 0.5s ease;
}

.demo-screen > div {
  transition: opacity 0.5s ease;
}

.demo-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: scan 3s ease-in-out infinite;
}

@keyframes scan {
  0% { left: -100%; }
  100% { left: 100%; }
}



/* Navigation */
nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

[data-theme="dark"] nav {
  background: rgba(17, 24, 39, 0.95);
}

nav.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px var(--shadow-color);
}

[data-theme="dark"] nav.scrolled {
  background: rgba(17, 24, 39, 0.98);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  height: 32px;
  width: auto;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--orange-500);
}

/* Features Section */
.features {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--bg-secondary);
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px var(--shadow-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.feature-card:hover,
.feature-card.fade-in.visible:hover {
  transform: translateY(-5px) scale(1.02) !important;
  box-shadow: 0 12px 40px var(--shadow-color-strong);
}

.feature-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  min-height: 120px;
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 4px;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
  line-height: 1.3;
  display: flex;
  align-items: center;
  min-height: 60px;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Demo Section */
.demo-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.demo-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.demo-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.demo-content > p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.demo-steps {
  margin: 2rem 0;
}

.demo-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
}

.demo-step-content {
  flex: 1;
}

.demo-step-content strong {
  display: block;
  color: var(--text-primary);
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.demo-step-content p {
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.step-number {
  background: var(--gradient-primary);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.demo-visual {
  background: var(--bg-primary);
  border-radius: 16px;
  padding: 2rem;
  position: relative;
}

.phone-mockup {
  background: var(--gray-900);
  border-radius: 24px;
  padding: 1rem;
  max-width: 300px;
  margin: 0 auto;
  position: relative;
}

.phone-screen {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 1.5rem;
  min-height: 400px;
  text-align: center;
}

.voice-indicator {
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: inline-block;
  animation: pulse 2s ease-in-out infinite;
}

.voice-command {
  margin-bottom: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.command-result {
  background: var(--bg-primary);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: left;
}

.result-item {
  color: var(--success);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.result-item:last-child {
  margin-bottom: 0;
}

.invoice-total {
  background: var(--gradient-primary);
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  margin-top: 1rem;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Pricing Section */
.pricing {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--warm-white) 0%, var(--light-peach) 100%);
}

[data-theme="dark"] .pricing {
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.pricing-card {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  position: relative;
  box-shadow: 0 8px 30px var(--shadow-color);
  transition: all 0.3s ease;
}

.pricing-card.featured {
  border: 2px solid var(--orange-500);
  transform: scale(1.05);
}

.pricing-card.featured::before {
  content: 'Most Popular';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 50px var(--shadow-color-strong);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.plan-price {
  font-size: 3rem;
  font-weight: 700;
  color: var(--orange-500);
  margin-bottom: 0.5rem;
}

.plan-price span {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 400;
}

.plan-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.plan-features {
  list-style: none;
  margin-bottom: 2rem;
}

.plan-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: 1.5rem;
}

.plan-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success);
  font-weight: 600;
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
  color: white;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Footer */
footer {
  background: var(--gray-950);
  color: var(--gray-100);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--orange-500);
}

.footer-section a {
  color: var(--gray-300);
  text-decoration: none;
  display: block;
  padding: 0.25rem 0;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--orange-500);
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  padding-top: 1rem;
  text-align: center;
  color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-container > div {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .demo-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }
}

/* Scroll animations */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .demo-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .nav-links {
    display: none;
  }

  .cta-buttons {
    justify-content: center;
  }
}
