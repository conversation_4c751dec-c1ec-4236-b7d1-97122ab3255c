.theme-toggle {
  background: transparent;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  min-width: 40px;
  min-height: 40px;
}

.theme-toggle:hover {
  border-color: var(--orange-500);
  color: var(--orange-500);
  background: var(--orange-50);
  transform: translateY(-1px);
}

[data-theme="dark"] .theme-toggle:hover {
  background: rgba(255, 107, 53, 0.1);
}

.theme-toggle:active {
  transform: translateY(0);
}

.theme-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.theme-toggle-icon.is-dark {
  transform: rotate(180deg);
}

.theme-toggle svg {
  transition: all 0.3s ease;
}

.theme-toggle:focus {
  outline: 2px solid var(--orange-500);
  outline-offset: 2px;
}

.theme-toggle:focus:not(:focus-visible) {
  outline: none;
}
